// ignore_for_file: constant_identifier_names

import 'package:hive/hive.dart';

class Prefobj {
  static Box? preferences; // Static reference to preferences box

  // static bool getSwipeTutorialShown() {
  //   return preferences?.get('swipe_tutorial_shown') == true;
  // }

  // static Future<void> setSwipeTutorialShown(bool value) async {
  //   await preferences?.put('swipe_tutorial_shown', value);
  // }
}

class Prefkeys {
  static const String AUTHTOKEN = 'auth_token';
  static const String LIGHTDARK = 'light_dark';
  static const String FOLLOW_SYSTEM = 'follow_system';
  static const String CALLER_IDENTIFIER = 'caller_identifier';
  static const String ONBOARDING = 'onboarding';
  static const String IS_LOGIN = 'is_login';
  static const String DRAFT_ORDER_ID = 'draft_order_id';
  static const String PROFILE_DATA = 'profile_data';
  static const String SWIPE_TUTORIAL_SHOWN = 'swipe_tutorial_shown';
}
