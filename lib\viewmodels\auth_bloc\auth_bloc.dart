import 'package:equatable/equatable.dart';
import 'package:room_eight/core/utils/app_exports.dart';
part 'auth_event.dart';
part 'auth_state.dart';

class AuthBloc extends Bloc<AuthEvent, AuthState> {
  static const int initialTime = 60;
  Timer? _timer;
  final AuthRepository authRepository;

  AuthBloc(this.authRepository)
    : super(
        AuthState(
          loginFormKey: GlobalKey<FormState>(),
          signupFormKey: GlobalKey<FormState>(),
          addPersonalDetailFormKey: GlobalKey<FormState>(),
          phoneController: TextEditingController(),
          phonefocusnode: FocusNode(),
          emailController: TextEditingController(),
          passwordController: TextEditingController(),
          emailFocusNode: FocusNode(),
          passwordFocusNode: FocusNode(),
          obscurePassword: true,
          signupEmailController: TextEditingController(),
          signupEmailfocusnode: FocusNode(),
          signupPasswordController: TextEditingController(),
          signupPasswordfocusnode: FocusNode(),
          signupConfirmPasswordController: TextEditingController(),
          signupConfirmPasswordfocusnode: FocusNode(),
          // fullNameController: TextEditingController(),
          // fullNameFocusNode: FocusNode(),
          // dobController: TextEditingController(),
          // dobFocusNode: FocusNode(),
        ),
      ) {
    on<LoginSubmitted>(_onLoginSubmitted);
    on<SignupSubmitted>(_onSignupSubmitted);
    on<EmailChanged>(_onEmailChanged);
    on<PasswordChanged>(_onPasswordChanged);
    on<TogglePasswordVisibility>(_onTogglePasswordVisibility);
    on<ToggleAgreementAccepted>(_onToggleAgreementAccepted);
    on<ToggleSignupPasswordVisibility>(_onToggleSignupPasswordVisibility);
    on<ToggleSignupConfirmPasswordVisibility>(
      _onToggleSignupConfirmPasswordVisibility,
    );
    on<SignupEmailChanged>(_onSignupEmailChanged);
    on<SignupPasswordChanged>(_onSignupPasswordChanged);
    on<SignupConfirmPasswordChanged>(_onSignupConfirmPasswordChanged);
    // on<FullNameChanged>(_onFullNameChanged);
    // on<DateOfBirthChanged>(_onDateOfBirthChanged);
  }

  Future<void> _onLoginSubmitted(
    LoginSubmitted event,
    Emitter<AuthState> emit,
  ) async {
    try {
      emit(state.copyWith(isloginLoading: true));
      // SendOtpModel sendOtpModel = await authRepository.sendOtpApiCall(
      //   countryCode: "+${state.selectedCountry.phoneCode}",
      //   phoneNumber: state.phoneNumber,
      // );
      Prefobj.preferences?.put(Prefkeys.IS_LOGIN, true);
      // RoomEight.show(
      //   message: "Login successfully.",
      //   type: ToastificationType.success,
      // );
      // emit(state.copyWith(sendOtpModel: sendOtpModel, isloginLoading: false));
      // _onStartTimer(StartTimerEvent(), emit);
      // if (!event.isresendOtp) {
      // NavigatorService.pushNamed(
      //   AppRoutes.otpVerificationScreen,
      //   arguments: [
      //     "+${state.selectedCountry.phoneCode}${state.phoneNumber}",
      //     sendOtpModel.phoneOTP,
      //   ],
      // );
      // state.phoneController?.clear();
      // }
      NavigatorService.pushNamed(AppRoutes.roomEightNavBar);
      // NavigatorService.pushNamed(AppRoutes.roomEightNavBar);

      emit(state.copyWith(isloginLoading: false));
    } catch (e) {
      Logger.lOG(e.toString());
      emit(state.copyWith(isloginLoading: false));
    }
  }

  Future<void> _onSignupSubmitted(
    SignupSubmitted event,
    Emitter<AuthState> emit,
  ) async {
    try {
      emit(state.copyWith(isloginLoading: true));

      // Prefobj.preferences?.put(Prefkeys.IS_LOGIN, true);
      // RoomEight.show(
      //   message: "Signup successfully.",
      //   type: ToastificationType.success,
      // );

      NavigatorService.pushNamed(AppRoutes.welcomeScreen);

      emit(state.copyWith(isloginLoading: false));
    } catch (e) {
      Logger.lOG(e.toString());
      emit(state.copyWith(isloginLoading: false));
    }
  }

  void _onEmailChanged(EmailChanged event, Emitter<AuthState> emit) {
    state.emailController?.text = event.email;
    // Optionally, emit a new state if you want to trigger rebuilds
    emit(state.copyWith(emailController: state.emailController));
  }

  void _onPasswordChanged(PasswordChanged event, Emitter<AuthState> emit) {
    state.passwordController?.text = event.password;
    emit(state.copyWith(passwordController: state.passwordController));
  }

  void _onTogglePasswordVisibility(
    TogglePasswordVisibility event,
    Emitter<AuthState> emit,
  ) {
    emit(state.copyWith(obscurePassword: !state.obscurePassword));
  }

  void _onToggleSignupPasswordVisibility(
    ToggleSignupPasswordVisibility event,
    Emitter<AuthState> emit,
  ) {
    emit(state.copyWith(signupObscurePassword: !state.signupObscurePassword));
  }

  void _onToggleSignupConfirmPasswordVisibility(
    ToggleSignupConfirmPasswordVisibility event,
    Emitter<AuthState> emit,
  ) {
    emit(
      state.copyWith(
        signupObscureConfirmPassword: !state.signupObscureConfirmPassword,
      ),
    );
  }

  FutureOr<void> _onToggleAgreementAccepted(
    ToggleAgreementAccepted event,
    Emitter<AuthState> emit,
  ) {
    emit(state.copyWith(isAgreementAccepted: !state.isAgreementAccepted));
  }

  void _onSignupEmailChanged(
    SignupEmailChanged event,
    Emitter<AuthState> emit,
  ) {
    state.signupEmailController?.text = event.email;
    emit(state.copyWith(signupEmailController: state.signupEmailController));
  }

  void _onSignupPasswordChanged(
    SignupPasswordChanged event,
    Emitter<AuthState> emit,
  ) {
    state.signupPasswordController?.text = event.password;
    emit(
      state.copyWith(signupPasswordController: state.signupPasswordController),
    );
  }

  void _onSignupConfirmPasswordChanged(
    SignupConfirmPasswordChanged event,
    Emitter<AuthState> emit,
  ) {
    state.signupConfirmPasswordController?.text = event.confirmPassword;
    emit(
      state.copyWith(
        signupConfirmPasswordController: state.signupConfirmPasswordController,
      ),
    );
  }

  // void _onFullNameChanged(FullNameChanged event, Emitter<AuthState> emit) {
  //   state.fullNameController?.text = event.fullName;
  //   emit(state.copyWith(fullNameController: state.fullNameController));
  // }

  // void _onDateOfBirthChanged(
  //   DateOfBirthChanged event,
  //   Emitter<AuthState> emit,
  // ) {
  //   state.dobController?.text = event.dateOfBirth;
  //   emit(state.copyWith(dobController: state.dobController));
  // }

  @override
  Future<void> close() {
    _timer?.cancel();
    return super.close();
  }
}
