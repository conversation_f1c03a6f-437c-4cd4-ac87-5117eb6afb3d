import 'package:room_eight/core/api_config/client/api_client.dart';

class AuthRepository {
  final ApiClient apiClient;
  AuthRepository({required this.apiClient});

  // Future<AgeModel> ageConsentCall({required String deviceId}) async {
  //   try {
  //     final Map<String, dynamic> data = {'deviceId': deviceId};
  //     var response = await apiClient.request(
  //       RequestType.POST,
  //       ApiEndPoint.ageConsentUrl,
  //       data: data,
  //     );
  //     return await compute(AgeModel.fromJson, response);
  //   } catch (error) {
  //     rethrow;
  //   }
  // }

 
}
