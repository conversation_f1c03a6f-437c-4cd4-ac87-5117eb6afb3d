import 'package:room_eight/core/flavor_config/flavor_config.dart';

class ApiEndPoint {
  static String get baseUrl => FlavorConfig.instance.env.baseUrl;

  static String get ageConsentUrl => '$baseUrl/api/SalesAppAuth/AgeConsent';
  static String get initializeUrl => '$baseUrl/api/SalesAppAuth/Initialize';
  static String get sendOtpUrl => '$baseUrl/api/SalesAppAuth/SendOTP';
  static String get createCustomerUrl => '$baseUrl/api/Customer/CreateCustomer';
  static String get getAddressUrl => '$baseUrl/api/Customer/GetAddressLatLong';
  static String get createCustomAddress =>
      '$baseUrl/api/Customer/CreateCustomerAddress';
  static String get addressAutoCompleUrl =>
      '$baseUrl/api/Customer/AddressAutoComplete';
  static String get getCustomerAddresssUrl =>
      '$baseUrl/api/Customer/GetCustomerAddressList';
  static String get getCollectionListUrl =>
      '$baseUrl/api/Catalog/GetCollectionList';
  static String get getProductListByCollectionUrl =>
      '$baseUrl/api/Catalog/GetProductListByCollection';
  static String get addtoCartUrl => '$baseUrl/api/Order/AddToCart';
  static String get getCartUrl => '$baseUrl/api/Order/GetCart';
  static String get clearCartUrl => '$baseUrl/api/Order/ClearCart';
  static String get getCustomerProfileUrl =>
      '$baseUrl/api/Customer/GetCustomerProfile';
  static String get getProductDetailbyIdUrl =>
      '$baseUrl/api/Catalog/GetProductDetailByProductId';
  static String get updateCartAddressUrl =>
      '$baseUrl/api/Order/UpdateCartAddress';
  static String get getproductListBySearchUrl =>
      '$baseUrl/api/Catalog/GetProductListBySearch';
  static String get getCouponListUrl => '$baseUrl/api/Coupon/GetCouponList';
  static String get addPaymentMethodCardUrl =>
      '$baseUrl/api/Checkout/AddPaymentMethodCard';
  static String get getDeliverySlotsUrl =>
      "$baseUrl/api/Checkout/GetDeliverySlots";
  static String get addPaymentMethodIdUrl =>
      "$baseUrl/api/Checkout/AddPaymentMethodId";
  static String get getPaymentMethodsUrl =>
      "$baseUrl/api/Checkout/GetPaymentMethods";
  static String get getCustomerOrderListUrl =>
      "$baseUrl/api/Customer/GetCustomerOrderList";
  static String get getCustomerOrderDetailUrl =>
      "$baseUrl/api/Customer/GetCustomerOrderDetail";
  static String get applyCouponUrl => "$baseUrl/api/Checkout/ApplyCoupon";
  static String get getCheckoutOrderUrl =>
      "$baseUrl/api/Checkout/GetCheckoutOrder";
  static String get applyDeliveryOptionUrl =>
      "$baseUrl/api/Checkout/ApplyDeliveryOption";
  static String get applyDeliveryTipUrl =>
      "$baseUrl/api/Checkout/ApplyDeliveryTip";
  static String get createOrderUrl => "$baseUrl/api/Checkout/CreateOrder";
  static String get homePageSectionsUrl =>
      "$baseUrl/api/UiLayout/HomePageSections";
}
