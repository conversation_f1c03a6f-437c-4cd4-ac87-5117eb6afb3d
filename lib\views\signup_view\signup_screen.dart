import 'dart:ui';
import 'package:room_eight/core/utils/app_exports.dart';

class SignupScreen extends StatelessWidget {
  const SignupScreen({super.key});

  static Widget builder(BuildContext context) => const SignupScreen();

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<AuthBloc, AuthState>(
      builder: (context, state) {
        return Scaffold(
          backgroundColor: Theme.of(context).customColors.fillColor,
          body: Form(
            key: state.signupFormKey,
            child: SingleChildScrollView(
              child: BackgroundImage(
                imagePath: Assets.images.pngs.other.pngSignupBg.path,
                child: ConstrainedBox(
                  constraints: BoxConstraints(
                    minHeight: MediaQuery.of(context).size.height,
                  ),
                  child: IntrinsicHeight(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        // _buildTopSection(context),
                        _buildSignupHeader(context),
                        _buildSignupForm(context, state),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  // Widget _buildTopSection(BuildContext context) {
  //   return Padding(
  //     padding: EdgeInsets.only(
  //       left: 16.w,
  //       right: 16.w,
  //       top: 60.h,
  //       bottom: 16.h,
  //     ),
  //     child: _buildTopBar(context),
  //   );
  // }

  // Widget _buildTopBar(BuildContext context) {
  //   final customColors = Theme.of(context).customColors;

  //   return Row(
  //     mainAxisAlignment: MainAxisAlignment.end,
  //     children: [
  //       CustomGradientContainer(
  //         height: 36.w,
  //         width: 36.w,
  //         topColor: customColors.fillColor!.withAlpha((0.6 * 255).toInt()),
  //         bottomColor: customColors.blackColor!.withAlpha((0.4 * 255).toInt()),
  //         fillColor: customColors.fillColor!.withAlpha(75),
  //         child: CustomImageView(
  //           imagePath: Assets.images.svgs.icons.icForwardArrow.path,
  //         ),
  //       ),
  //     ],
  //   );
  // }

  Widget _buildSignupHeader(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(
        left: 38.w,
        right: 38.w,
        top: 126.h,
        bottom: 32.h,
      ),
      child: Column(
        children: [
          Text(
            Lang.of(context).lbl_signup_message,
            textAlign: TextAlign.center,
            style: Theme.of(context).textTheme.labelLarge?.copyWith(
              fontSize: 16.sp,
              color: Theme.of(context).customColors.fillColor,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSignupForm(BuildContext context, AuthState state) {
    return Flexible(
      child: ClipRRect(
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(20),
          topRight: Radius.circular(20),
        ),
        child: BackdropFilter(
          filter: ImageFilter.blur(sigmaX: 7, sigmaY: 7),
          child: Container(
            width: double.infinity,
            constraints: BoxConstraints(
              minHeight: MediaQuery.of(context).size.height * 0.6,
            ),
            decoration: BoxDecoration(
              color: Theme.of(
                context,
              ).customColors.fillColor?.withValues(alpha: 0.8),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(20),
                topRight: Radius.circular(20),
              ),
            ),
            child: Padding(
              padding: EdgeInsets.all(16.w),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  _builSignupTitle(context),
                  buildSizedBoxH(24.h),
                  _buildSignupNameField(context, state),
                  buildSizedBoxH(16.h),
                  _buildSignupEmailField(context, state),
                  buildSizedBoxH(16.h),
                  _buildSignupPasswordField(context, state),
                  buildSizedBoxH(16.h),
                  _buildsignupConfPasswordField(context, state),
                  buildSizedBoxH(4.h),
                  _buildForgotPassword(context),
                  buildSizedBoxH(24.h),
                  const Spacer(),
                  _buildSignupButton(context, state),
                  buildSizedBoxH(16.h),
                  _buildSignUpOption(context),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _builSignupTitle(BuildContext context) {
    return Text(
      Lang.of(context).lbl_sign_up,
      textAlign: TextAlign.center,
      style: Theme.of(context).textTheme.labelLarge?.copyWith(
        fontSize: 22.sp,
        color: Theme.of(context).customColors.blackColor,
        fontWeight: FontWeight.bold,
      ),
    );
  }

  Widget _buildSignupNameField(BuildContext context, AuthState state) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Full Name',
          style: Theme.of(context).textTheme.labelLarge?.copyWith(
            fontSize: 16.sp,
            color: Theme.of(context).customColors.blackColor,
          ),
        ),
        CustomTextInputField(
          context: context,
          type: InputType.text,
          hintLabel: 'Enter your full name',
          controller: state.signupNameController,
          focusNode: state.signupNamefocusnode,
          textInputAction: TextInputAction.next,
          isCapitalized: true,
          validator: (value) => AppValidations.nameValidation(value, context),
          onChanged: (value) =>
              context.read<AuthBloc>().add(SignupNameChanged(value)),
          prefixIcon: CustomImageView(
            margin: EdgeInsets.all(14.0),
            imagePath: Assets.images.svgs.icons.icProfile.path,
          ),
        ),
      ],
    );
  }

  Widget _buildSignupEmailField(BuildContext context, AuthState state) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'School ID',
          style: Theme.of(context).textTheme.labelLarge?.copyWith(
            fontSize: 16.sp,
            color: Theme.of(context).customColors.blackColor,
          ),
        ),
        CustomTextInputField(
          context: context,
          type: InputType.email,
          hintLabel: Lang.of(context).lbl_emptyEmail,
          controller: state.signupEmailController,
          focusNode: state.signupEmailfocusnode,
          textInputAction: TextInputAction.next,
          prefixIcon: CustomImageView(
            margin: EdgeInsets.all(14.0),
            imagePath: Assets.images.svgs.icons.icMail.path,
          ),
          validator: (value) => AppValidations.emailValidation(value, context),
          onChanged: (value) =>
              context.read<AuthBloc>().add(SignupEmailChanged(value)),
        ),
      ],
    );
  }

  Widget _buildSignupPasswordField(BuildContext context, AuthState state) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          Lang.of(context).lbl_password,
          style: Theme.of(context).textTheme.labelLarge?.copyWith(
            fontSize: 16.sp,
            color: Theme.of(context).customColors.blackColor,
          ),
        ),
        CustomTextInputField(
          context: context,
          type: InputType.password,
          hintLabel: Lang.of(context).lbl_emptyPassword,
          controller: state.signupPasswordController,
          focusNode: state.signupPasswordfocusnode,
          textInputAction: TextInputAction.done,
          obscureText: state.signupObscurePassword,
          validator: (value) =>
              AppValidations.passwordValidation(value, context),
          onChanged: (value) =>
              context.read<AuthBloc>().add(SignupPasswordChanged(value)),
          prefixIcon: CustomImageView(
            margin: EdgeInsets.all(14.r),
            imagePath: Assets.images.svgs.icons.icLock.path,
          ),

          suffixIcon: IconButton(
            icon: Icon(
              state.signupObscurePassword
                  ? Icons.visibility_off
                  : Icons.visibility,
            ),
            onPressed: () {
              context.read<AuthBloc>().add(ToggleSignupPasswordVisibility());
            },
          ),
        ),
      ],
    );
  }

  Widget _buildsignupConfPasswordField(BuildContext context, AuthState state) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          Lang.of(context).lbl_conf_password,
          style: Theme.of(context).textTheme.labelLarge?.copyWith(
            fontSize: 16.sp,
            color: Theme.of(context).customColors.blackColor,
          ),
        ),
        CustomTextInputField(
          context: context,
          type: InputType.password,
          hintLabel: Lang.of(context).lbl_emptyConfPassword,
          controller: state.signupConfirmPasswordController,
          focusNode: state.signupConfirmPasswordfocusnode,
          textInputAction: TextInputAction.done,
          obscureText: state.signupObscureConfirmPassword,
          prefixIcon: CustomImageView(
            margin: EdgeInsets.all(14.r),
            imagePath: Assets.images.svgs.icons.icLock.path,
          ),
          validator: (value) =>
              AppValidations.confPasswordValidation(value, context),
          onChanged: (value) =>
              context.read<AuthBloc>().add(SignupConfirmPasswordChanged(value)),

          suffixIcon: IconButton(
            icon: Icon(
              state.signupObscureConfirmPassword
                  ? Icons.visibility_off
                  : Icons.visibility,
            ),
            onPressed: () {
              context.read<AuthBloc>().add(
                ToggleSignupConfirmPasswordVisibility(),
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _buildForgotPassword(BuildContext context) {
    return Align(
      alignment: Alignment.centerRight,
      child: TextButton(
        onPressed: () {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(Lang.of(context).lbl_forgot_password_clicked),
            ),
          );
        },
        child: Text(
          Lang.of(context).lbl_forgot_password,
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            color: Theme.of(context).customColors.primaryColor,
            fontWeight: FontWeight.w500,
          ),
        ),
      ),
    );
  }

  bool _passwordsMatch(AuthState state) {
    final password = state.signupPasswordController?.text ?? '';
    final confirmPassword = state.signupConfirmPasswordController?.text ?? '';
    return password.isNotEmpty &&
        confirmPassword.isNotEmpty &&
        password == confirmPassword;
  }

  Widget _buildSignupButton(BuildContext context, AuthState state) {
    final passwordsMatch = _passwordsMatch(state);
    final hasPassword = (state.signupPasswordController?.text ?? '').isNotEmpty;
    final hasConfirmPassword =
        (state.signupConfirmPasswordController?.text ?? '').isNotEmpty;

    return Column(
      children: [
        // Show password mismatch warning
        if (hasPassword && hasConfirmPassword && !passwordsMatch)
          Container(
            padding: EdgeInsets.symmetric(vertical: 8.h, horizontal: 16.w),
            margin: EdgeInsets.only(bottom: 16.h),
            decoration: BoxDecoration(
              color: Colors.red.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8.r),
              border: Border.all(color: Colors.red.withValues(alpha: 0.3)),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.warning_amber_rounded,
                  color: Colors.red,
                  size: 16.sp,
                ),
                buildSizedboxW(8.w),
                Expanded(
                  child: Text(
                    'Passwords do not match',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Colors.red,
                      fontSize: 12.sp,
                    ),
                  ),
                ),
              ],
            ),
          ),
        CustomElevatedButton(
          isLoading: state.isSignupLoading,
          isDisabled: state.isSignupLoading || !passwordsMatch,
          text: Lang.of(context).lbl_sign_up,
          buttonTextStyle: Theme.of(context).textTheme.bodyLarge?.copyWith(
            color: Theme.of(context).customColors.fillColor,
            fontSize: 18.0.sp,
            fontWeight: FontWeight.w500,
          ),
          onPressed: () {
            FocusManager.instance.primaryFocus?.unfocus();
            if (passwordsMatch &&
                (state.signupFormKey.currentState?.validate() ?? false)) {
              context.read<AuthBloc>().add(SignupSubmitted());
            }
          },
        ),
      ],
    );
  }

  Widget _buildSignUpOption(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Text(
          Lang.of(context).lbl_login_redirect,
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            color: Theme.of(context).customColors.blackColor,
            fontSize: 14.sp,
          ),
        ),
        TextButton(
          onPressed: () {
            NavigatorService.pushAndRemoveUntil(AppRoutes.loginScreen);
          },
          style: TextButton.styleFrom(
            padding: EdgeInsets.zero,
            minimumSize: Size.zero,
            tapTargetSize: MaterialTapTargetSize.shrinkWrap,
          ),
          child: Text(
            Lang.of(context).lbl_login,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Theme.of(context).customColors.primaryColor,
              fontSize: 14.sp,
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
      ],
    );
  }
}
